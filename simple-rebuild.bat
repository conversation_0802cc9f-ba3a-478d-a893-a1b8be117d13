@echo off
REM Simple rebuild script

echo Rebuilding Confluence MCP...

set "JAVA_HOME=C:\Program Files\Java\jdk-17.0.1"

echo JAVA_HOME: %JAVA_HOME%

call mvnw.cmd clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!

echo Testing startup...
set CONFLUENCE_USERNAME=test
set CONFLUENCE_PASSWORD=test

java -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar --help

pause
