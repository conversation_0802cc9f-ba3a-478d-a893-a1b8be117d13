@echo off
REM 测试启动脚本

echo Testing Confluence MCP startup...
echo.

REM 设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1

echo JAVA_HOME: %JAVA_HOME%
echo.

REM 重新构建项目
echo Rebuilding project...
call mvnw.cmd clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build successful! Testing startup...
echo.

REM 设置测试环境变量
set CONFLUENCE_USERNAME=test_user
set CONFLUENCE_PASSWORD=test_password

REM 启动应用（只运行几秒钟）
echo Starting application for 10 seconds...
timeout /t 10 /nobreak | java -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

echo.
echo Startup test completed.
pause
