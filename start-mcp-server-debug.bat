@echo off
REM Confluence MCP Server调试启动脚本

echo ========================================
echo Confluence MCP Server 调试模式启动
echo ========================================
echo.

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 错误: JAR文件不存在
    echo 请先运行构建: simple-rebuild.bat
    pause
    exit /b 1
)

REM 检查环境变量
if "%CONFLUENCE_USERNAME%"=="" (
    echo 警告: CONFLUENCE_USERNAME 环境变量未设置
    echo.
    echo 选项:
    echo 1. 运行配置向导: configure-confluence.bat
    echo 2. 手动设置环境变量: call confluence-env.bat
    echo 3. 继续使用测试配置
    echo.
    
    set /p ENV_CHOICE="请选择 (1-3): "
    
    if "%ENV_CHOICE%"=="1" (
        call configure-confluence.bat
        if %ERRORLEVEL% neq 0 exit /b 1
    ) else if "%ENV_CHOICE%"=="2" (
        if exist "confluence-env.bat" (
            call confluence-env.bat
        ) else (
            echo confluence-env.bat 文件不存在
            echo 请先运行: configure-confluence.bat
            pause
            exit /b 1
        )
    ) else (
        echo 使用测试配置...
        set CONFLUENCE_USERNAME=test_user
        set CONFLUENCE_PASSWORD=test_password
    )
)

echo.
echo 当前配置:
echo ----------------------------------------
echo - 用户名: %CONFLUENCE_USERNAME%
echo - 服务器: %CONFLUENCE_URL%
echo - 传输模式: STDIO
echo - 日志级别: DEBUG
echo - Web应用类型: NONE
echo.

echo 调试功能:
echo ----------------------------------------
echo - 详细的连接日志
echo - Confluence API调用跟踪
echo - Spring Boot启动信息
echo - MCP协议交互日志
echo.

echo 正在启动MCP服务器 (调试模式)...
echo 按 Ctrl+C 停止服务器
echo.

REM 启动MCP服务器（调试模式，带详细日志）
java -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -Dlogging.level.com.qccconfluencemcp=DEBUG ^
     -Dlogging.level.org.springframework.ai=DEBUG ^
     -Dlogging.level.org.springframework.web.reactive.function.client=DEBUG ^
     -Dlogging.level.reactor.netty.http.client=DEBUG ^
     -Dlogging.pattern.console="%%d{yyyy-MM-dd HH:mm:ss.SSS} [%%thread] %%-5level %%logger{36} - %%msg%%n" ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

echo.
echo ========================================
echo MCP服务器已停止
echo ========================================
echo.

echo 如果遇到问题，请检查上面的日志信息:
echo.
echo 常见问题和解决方案:
echo.
echo 1. 连接被拒绝 (Connection refused):
echo    - 检查服务器地址是否正确
echo    - 确认网络连接正常
echo    - 检查防火墙设置
echo.
echo 2. 认证失败 (401 Unauthorized):
echo    - 验证用户名和密码
echo    - 如果使用API Token，确认令牌有效
echo    - 检查账户是否被锁定
echo.
echo 3. 权限不足 (403 Forbidden):
echo    - 确认账户有访问Confluence的权限
echo    - 检查空间访问权限
echo.
echo 4. 页面未找到 (404 Not Found):
echo    - 检查Confluence服务器地址
echo    - 确认API端点路径正确
echo.
echo 5. SSL/TLS错误:
echo    - 检查证书配置
echo    - 考虑禁用SSL验证（仅测试环境）
echo.

pause
