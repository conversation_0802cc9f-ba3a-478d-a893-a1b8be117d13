# Confluence MCP Server

基于Spring AI MCP Server Boot Starter构建的Confluence集成服务器，提供Model Context Protocol (MCP)接口来访问Confluence文档。

## 功能特性

- **页面搜索**: 支持关键词搜索Confluence页面
- **页面内容获取**: 根据页面ID获取详细内容
- **空间信息查询**: 获取Confluence空间的详细信息
- **空间页面列表**: 获取指定空间下的所有页面
- **STDIO传输**: 支持标准输入输出传输模式
- **认证支持**: 支持基础认证和API Token

## 技术栈

- Spring Boot 3.4.1
- Spring AI 1.0.1
- Spring WebFlux
- Model Context Protocol (MCP)
- Jackson JSON处理

## 快速开始

### 前置要求

- Java 17或更高版本
- Maven 3.6或更高版本
- Confluence服务器访问权限

### 构建项目

```bash
./mvnw clean install -DskipTests
```

### 配置

在`src/main/resources/application.yml`中配置Confluence连接信息：

```yaml
confluence:
  base-url: https://your-confluence-server.com
  username: ${CONFLUENCE_USERNAME:your-username}
  password: ${CONFLUENCE_PASSWORD:your-password}
  connect-timeout: 30000
  read-timeout: 60000
  ssl-verification: true
```

### 运行服务器

#### STDIO模式（推荐用于MCP客户端）

```bash
java -Dspring.ai.mcp.server.transport=STDIO \
     -Dspring.main.web-application-type=none \
     -Dlogging.pattern.console= \
     -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

#### 使用启动脚本

Windows:
```bash
start-mcp-server.bat
```

Linux/Mac:
```bash
chmod +x start-mcp-server.sh
./start-mcp-server.sh
```

## 可用工具

### 1. searchPages
搜索Confluence页面内容

**参数:**
- `query` (String): 搜索关键词
- `limit` (int): 返回结果数量限制，默认10，最大50

**示例:**
```json
{
  "query": "Spring Boot",
  "limit": 10
}
```

### 2. getPageContent
根据页面ID获取页面详细内容

**参数:**
- `pageId` (String): 页面ID

**示例:**
```json
{
  "pageId": "123456"
}
```

### 3. getSpaceInfo
获取Confluence空间的详细信息

**参数:**
- `spaceKey` (String): 空间键值

**示例:**
```json
{
  "spaceKey": "DEMO"
}
```

### 4. getSpacePages
获取指定空间下的所有页面列表

**参数:**
- `spaceKey` (String): 空间键值
- `limit` (int): 返回结果数量限制，默认25，最大100

**示例:**
```json
{
  "spaceKey": "DEMO",
  "limit": 25
}
```

## 客户端集成

### 使用MCP客户端连接

```bash
# 通过STDIO连接到服务器
java -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

### 在AI应用中使用

可以将此MCP服务器集成到支持MCP协议的AI应用中，如Claude Desktop、VS Code扩展等。

## 配置选项

### Confluence配置

```yaml
confluence:
  base-url: https://your-confluence-instance.com  # Confluence服务器地址
  username: your_username                          # 用户名
  password: your_password                          # 密码或API Token
  connect-timeout: 30000                          # 连接超时（毫秒）
  read-timeout: 60000                             # 读取超时（毫秒）
  ssl-verification: true                          # 是否启用SSL验证
```

### 日志配置

```yaml
logging:
  level:
    com.qccconfluencemcp: INFO
    io.modelcontextprotocol: DEBUG
```

## 安全注意事项

1. **认证信息保护**: 不要在代码中硬编码用户名和密码，使用环境变量或安全的配置管理
2. **网络安全**: 确保Confluence服务器的网络访问安全
3. **权限控制**: 使用具有适当权限的Confluence账户

## 多实例运行

### 问题说明
如果您遇到"同一个MCP包只能运行一次配置"的问题，即本地多个项目配置只有一个可以运行，可以使用以下解决方案。

### 快速解决方案

1. **运行快速配置脚本**:
   ```bash
   quick-multi-setup.bat
   ```

2. **输入项目数量**: 根据需要输入要创建的项目配置数量

3. **使用生成的配置**:
   - 使用 `multi-configs\mcp-multi-config.json` 作为合并配置
   - 或使用独立的 `mcp-projectX-config.json` 配置

### 高级管理

使用多实例管理器进行更精细的控制：
```bash
manage-multiple-instances.bat
```

### 配置示例

多实例配置示例：
```json
{
  "mcpServers": {
    "confluence-mcp-project1": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-DMCP_SERVER_NAME=confluence-mcp-project1",
        "-DMCP_INSTANCE_ID=project1-instance",
        "-Dserver.port=0",
        "-Dspring.jmx.enabled=false",
        "-jar", "path/to/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "%CONFLUENCE_USERNAME%",
        "CONFLUENCE_PASSWORD": "%CONFLUENCE_PASSWORD%"
      }
    },
    "confluence-mcp-project2": {
      // 类似配置...
    }
  }
}
```

## 故障排除

### 常见问题

1. **连接失败**: 检查Confluence服务器地址和网络连接
2. **认证失败**: 验证用户名和密码是否正确
3. **页面访问失败**: 确认用户有访问指定页面的权限
4. **多实例冲突**: 使用上述多实例解决方案

### 日志调试

启用调试日志：

```yaml
logging:
  level:
    com.qccconfluencemcp: DEBUG
    io.modelcontextprotocol: DEBUG
```

## 开发

### 项目结构

```
src/main/java/com/qccconfluencemcp/
├── config/                 # 配置类
│   └── ConfluenceProperties.java
├── model/                  # 数据模型
│   ├── ConfluencePage.java
│   └── ConfluenceSearchResult.java
├── service/                # 服务层
│   └── ConfluenceClient.java
├── mcp/                    # MCP相关
│   ├── ConfluenceTools.java
│   └── ConfluenceMcpServer.java
└── QccConfluenceMcpApplication.java
```

### 扩展功能

可以通过以下方式扩展功能：

1. 在 `ConfluenceTools` 中添加新的工具
2. 在 `ConfluenceClient` 中添加新的API调用
3. 添加新的数据模型支持更多Confluence功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
