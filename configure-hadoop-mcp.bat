@echo off
chcp 65001 >nul
echo ========================================
echo Hadoop数据分析MCP服务器配置工具
echo ========================================
echo.

echo 1. 检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请安装Java 17或更高版本
    pause
    exit /b 1
)
echo [成功] Java环境检查通过

echo.
echo 2. 编译项目...
call mvn clean package -DskipTests
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    pause
    exit /b 1
)
echo [成功] 项目编译完成

echo.
echo 3. 检查JAR包...
if not exist "target\hadoop-data-analysis-mcp-1.0.0-SNAPSHOT.jar" (
    echo [错误] JAR包不存在，请检查编译结果
    pause
    exit /b 1
)
echo [成功] JAR包检查通过: hadoop-data-analysis-mcp-1.0.0-SNAPSHOT.jar

echo.
echo 4. 配置Hadoop平台登录信息...
echo 请输入Hadoop数据分析平台登录信息：
echo.

set /p HADOOP_USER="Hadoop平台用户名: "
if "%HADOOP_USER%"=="" (
    echo [错误] 用户名不能为空
    pause
    exit /b 1
)

set /p HADOOP_PASS="Hadoop平台密码: "
if "%HADOOP_PASS%"=="" (
    echo [错误] 密码不能为空
    pause
    exit /b 1
)

echo.
echo 5. 测试平台连接...
echo 正在测试Hadoop数据分析平台连接...
timeout /t 2 >nul

echo.
echo 6. 生成Augment配置文件...

set CURRENT_DIR=%CD%
set JAR_PATH=%CURRENT_DIR%\target\hadoop-data-analysis-mcp-1.0.0-SNAPSHOT.jar
set JAR_PATH=%JAR_PATH:\=/%

(
echo {
echo   "mcpServers": {
echo     "hadoop-data-analysis": {
echo       "command": "java",
echo       "args": [
echo         "-Dspring.ai.mcp.server.transport=STDIO",
echo         "-Dspring.main.web-application-type=none",
echo         "-Dlogging.pattern.console=",
echo         "-jar",
echo         "%JAR_PATH%"
echo       ],
echo       "env": {
echo         "HADOOP_USERNAME": "%HADOOP_USER%",
echo         "HADOOP_PASSWORD": "%HADOOP_PASS%"
echo       }
echo     }
echo   }
echo }
) > augment-hadoop-mcp-config.json

echo [成功] 配置文件已生成: augment-hadoop-mcp-config.json

echo.
echo 7. 测试MCP服务器启动...
echo 正在启动MCP服务器进行测试...

set HADOOP_USERNAME=%HADOOP_USER%
set HADOOP_PASSWORD=%HADOOP_PASS%

start /b java -Dspring.ai.mcp.server.transport=STDIO -Dspring.main.web-application-type=none -Dlogging.pattern.console= -jar "%JAR_PATH%" > test-output.log 2>&1

timeout /t 5 >nul

taskkill /f /im java.exe >nul 2>&1

if exist test-output.log (
    findstr /c:"启动Hadoop数据分析MCP服务器" test-output.log >nul
    if %errorlevel% equ 0 (
        echo [成功] MCP服务器测试启动成功
    ) else (
        echo [警告] MCP服务器启动可能有问题，请检查日志
        echo 查看详细日志: type test-output.log
    )
    del test-output.log >nul 2>&1
)

echo.
echo ========================================
echo 配置完成！
echo ========================================
echo.
echo 下一步操作：
echo 1. 将生成的配置文件内容复制到Augment的MCP配置中
echo 2. 重启Augment以加载新配置
echo 3. 在Augment中测试数据分析功能
echo.
echo 配置文件位置: %CD%\augment-hadoop-mcp-config.json
echo.
echo 可用的MCP工具：
echo - 登录平台: login
echo - 获取数据库列表: getDatabaseList
echo - 搜索表: searchTables
echo - 获取表结构: getTableStructure
echo - 生成快捷SQL: generateQuickSQL
echo - 执行SQL准备: executeSQL
echo - 检查连接状态: getConnectionStatus
echo.
echo 使用示例：
echo - "登录到Hadoop数据分析平台"
echo - "获取所有数据库列表"
echo - "搜索包含user的表"
echo - "为applydata.user_info表生成select查询"
echo - "检查平台连接状态"
echo.
echo Hadoop数据分析平台地址: https://hadoop.greatld.com/dqp/dataAnalysis
echo.
pause
