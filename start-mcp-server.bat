@echo off
REM Confluence MCP Server启动脚本

echo ========================================
echo Confluence MCP Server 启动
echo ========================================
echo.

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 错误: JAR文件不存在
    echo 请先运行构建: simple-rebuild.bat
    pause
    exit /b 1
)

REM 尝试加载配置文件
if exist "confluence-env.bat" (
    echo 正在加载配置文件: confluence-env.bat
    call confluence-env.bat
    echo ✓ 配置已加载
    echo.
)

REM 检查环境变量是否设置
if "%CONFLUENCE_USERNAME%"=="" (
    echo 警告: 未找到Confluence连接配置
    echo.
    echo 配置选项:
    echo 1. 运行配置向导 (推荐)
    echo 2. 手动输入配置
    echo 3. 退出
    echo.

    set /p CONFIG_CHOICE="请选择 (1-3): "

    if "%CONFIG_CHOICE%"=="1" (
        echo 启动配置向导...
        call configure-confluence.bat
        if %ERRORLEVEL% neq 0 (
            echo 配置失败
            pause
            exit /b 1
        )
        REM 重新加载配置
        if exist "confluence-env.bat" call confluence-env.bat
    ) else if "%CONFIG_CHOICE%"=="2" (
        echo.
        set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "
        set /p CONFLUENCE_PASSWORD="请输入Confluence密码或API Token: "
    ) else (
        echo 已取消启动
        pause
        exit /b 0
    )
)

echo 启动配置:
echo - 用户名: %CONFLUENCE_USERNAME%
echo - 服务器: https://doc.greatld.com
echo - 传输模式: STDIO
echo.

echo 正在启动MCP服务器...
echo 按 Ctrl+C 停止服务器
echo.

REM 启动MCP服务器（STDIO模式）
java -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -Dlogging.pattern.console= ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

echo.
echo MCP服务器已停止
pause
