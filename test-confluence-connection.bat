@echo off
REM Confluence连接测试脚本

echo ========================================
echo Confluence 连接测试
echo ========================================
echo.

REM 检查环境变量是否设置
if "%CONFLUENCE_USERNAME%"=="" (
    echo 错误: CONFLUENCE_USERNAME 环境变量未设置
    echo.
    echo 请先运行配置向导: configure-confluence.bat
    echo 或手动设置环境变量: call confluence-env.bat
    echo.
    pause
    exit /b 1
)

if "%CONFLUENCE_PASSWORD%"=="" (
    echo 错误: CONFLUENCE_PASSWORD 环境变量未设置
    echo.
    echo 请先运行配置向导: configure-confluence.bat
    echo 或手动设置环境变量: call confluence-env.bat
    echo.
    pause
    exit /b 1
)

echo 当前配置:
echo - 用户名: %CONFLUENCE_USERNAME%
echo - 服务器: %CONFLUENCE_URL%
echo - 密码: [已设置]
echo.

REM 检查JAR文件
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo JAR文件不存在，正在构建...
    call simple-rebuild.bat
    if %ERRORLEVEL% neq 0 (
        echo 构建失败
        pause
        exit /b 1
    )
)

echo 测试选项:
echo 1. 快速连接测试 (5秒)
echo 2. 详细连接测试 (15秒)
echo 3. 交互式测试 (手动停止)
echo.

set /p TEST_TYPE="请选择测试类型 (1-3): "

if "%TEST_TYPE%"=="1" set TEST_DURATION=5
if "%TEST_TYPE%"=="2" set TEST_DURATION=15
if "%TEST_TYPE%"=="3" set TEST_DURATION=0

echo.
echo 正在启动MCP服务器进行连接测试...

if "%TEST_DURATION%"=="0" (
    echo 按 Ctrl+C 停止测试
) else (
    echo 测试将在 %TEST_DURATION% 秒后自动停止
)

echo.
echo 查看启动日志中的以下信息:
echo - "Started ConfluenceMcpApplication" = 启动成功
echo - "Connection refused" 或 "timeout" = 网络问题
echo - "401 Unauthorized" = 认证失败
echo - "403 Forbidden" = 权限不足
echo - "404 Not Found" = 服务器地址错误
echo.

REM 启动MCP服务器
if "%TEST_DURATION%"=="0" (
    REM 交互式模式
    java -Dspring.ai.mcp.server.transport=STDIO ^
         -Dspring.main.web-application-type=none ^
         -Dlogging.level.com.qccconfluencemcp=DEBUG ^
         -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
         -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
         -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
) else (
    REM 定时模式
    start /b java -Dspring.ai.mcp.server.transport=STDIO ^
         -Dspring.main.web-application-type=none ^
         -Dlogging.level.com.qccconfluencemcp=DEBUG ^
         -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
         -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
         -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
    
    timeout /t %TEST_DURATION% /nobreak >nul
    taskkill /f /im java.exe >nul 2>&1
)

echo.
echo ========================================
echo 连接测试完成
echo ========================================
echo.

echo 结果分析:
echo.
echo 如果看到 "Started ConfluenceMcpApplication":
echo   ✓ 连接成功！MCP服务器已准备就绪
echo.
echo 如果看到认证错误:
echo   ✗ 检查用户名和密码/API Token是否正确
echo   ✗ 确认账户有访问Confluence的权限
echo.
echo 如果看到网络错误:
echo   ✗ 检查服务器地址是否正确
echo   ✗ 确认网络连接和防火墙设置
echo.
echo 如果看到其他错误:
echo   ✗ 查看完整错误信息
echo   ✗ 检查Confluence服务器状态
echo.

echo 故障排除建议:
echo.
echo 1. 重新配置连接信息:
echo    configure-confluence.bat
echo.
echo 2. 查看详细日志:
echo    start-mcp-server-debug.bat
echo.
echo 3. 检查Confluence服务器状态:
echo    在浏览器中访问: %CONFLUENCE_URL%
echo.

pause
