@echo off
chcp 65001 >nul
REM Simple Confluence Configuration Setup

echo ========================================
echo Confluence MCP Configuration Setup
echo ========================================
echo.

echo This script will help you configure Confluence connection.
echo.

REM Get Confluence server URL
set /p CONFLUENCE_URL="Enter Confluence server URL (default: https://doc.greatld.com): "
if "%CONFLUENCE_URL%"=="" set CONFLUENCE_URL=https://doc.greatld.com

echo.
REM Get username
set /p CONFLUENCE_USERNAME="Enter Confluence username: "

echo.
REM Get password/token
echo Choose authentication method:
echo 1. Password
echo 2. API Token (recommended)
echo.
set /p AUTH_TYPE="Select (1 or 2): "

if "%AUTH_TYPE%"=="2" (
    echo.
    echo Using API Token authentication
    echo How to get API Token:
    echo 1. Login to Confluence
    echo 2. Go to Account Settings
    echo 3. Security ^> Create and manage API tokens
    echo 4. Create new token
    echo.
    set /p CONFLUENCE_PASSWORD="Enter API Token: "
) else (
    set /p CONFLUENCE_PASSWORD="Enter password: "
)

echo.
echo Configuration Summary:
echo ----------------------
echo Server: %CONFLUENCE_URL%
echo Username: %CONFLUENCE_USERNAME%
if "%AUTH_TYPE%"=="2" (
    echo Auth: API Token
) else (
    echo Auth: Password
)
echo.

set /p CONFIRM="Is this correct? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo Configuration cancelled.
    pause
    exit /b 0
)

echo.
echo Saving configuration...

REM Create environment file
(
    echo @echo off
    echo REM Confluence Configuration
    echo set CONFLUENCE_USERNAME=%CONFLUENCE_USERNAME%
    echo set CONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD%
    echo set CONFLUENCE_URL=%CONFLUENCE_URL%
    echo.
    echo echo Confluence environment variables loaded:
    echo echo - Username: %%CONFLUENCE_USERNAME%%
    echo echo - Server: %%CONFLUENCE_URL%%
) > confluence-env.bat

echo Configuration saved to: confluence-env.bat

echo.
echo Testing configuration...
call confluence-env.bat

echo.
echo Next steps:
echo 1. Test connection: test-confluence-connection.bat
echo 2. Start MCP server: start-mcp-server.bat
echo 3. Setup multiple instances: quick-multi-setup.bat

pause
