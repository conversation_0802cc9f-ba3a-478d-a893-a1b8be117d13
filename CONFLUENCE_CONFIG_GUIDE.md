# Confluence MCP 连接配置指南

## 概述

本指南将帮助您配置Confluence MCP服务器的连接信息，包括服务器地址、认证信息和连接参数。

## 快速配置

### 方法1：使用配置向导（推荐）

运行自动配置向导：
```bash
configure-confluence.bat
```

向导将引导您完成以下步骤：
1. 设置Confluence服务器地址
2. 选择认证方式（密码或API Token）
3. 输入认证信息
4. 测试连接
5. 保存配置

### 方法2：手动配置环境变量

创建或编辑 `confluence-env.bat` 文件：
```batch
@echo off
set CONFLUENCE_USERNAME=your_username
set CONFLUENCE_PASSWORD=your_password_or_token
set CONFLUENCE_URL=https://your-confluence-server.com
```

然后运行：
```bash
call confluence-env.bat
```

## 认证配置

### 选项1：API Token认证（推荐）

API Token比密码更安全，推荐在生产环境使用。

**获取API Token步骤：**
1. 登录Confluence
2. 点击右上角头像 → 账户设置
3. 选择"安全" → "创建和管理API令牌"
4. 点击"创建API令牌"
5. 输入标签名称，点击"创建"
6. 复制生成的令牌

**配置示例：**
```batch
set CONFLUENCE_USERNAME=<EMAIL>
set CONFLUENCE_PASSWORD=ATATTxxxxxxxxxxxxxxxxxxx
```

### 选项2：用户名密码认证

**配置示例：**
```batch
set CONFLUENCE_USERNAME=john.doe
set CONFLUENCE_PASSWORD=your_password
```

## 服务器配置

### 默认配置

默认连接到 `https://doc.greatld.com`，如需修改：

```batch
set CONFLUENCE_URL=https://your-confluence-server.com
```

### 支持的URL格式

- `https://company.atlassian.net` (Atlassian Cloud)
- `https://confluence.company.com` (自托管)
- `http://localhost:8090` (本地开发)

## 连接参数

### 超时设置

在 `application.yml` 中配置：
```yaml
confluence:
  connect-timeout: 30000  # 连接超时（毫秒）
  read-timeout: 60000     # 读取超时（毫秒）
```

### SSL验证

```yaml
confluence:
  ssl-verification: true  # 生产环境建议启用
```

如果遇到SSL证书问题，可以临时禁用（仅测试环境）：
```batch
set CONFLUENCE_SSL_VERIFY=false
```

### 重试配置

```yaml
confluence:
  retry:
    max-attempts: 3  # 最大重试次数
    delay: 1000      # 重试延迟（毫秒）
```

## 测试连接

### 快速测试

```bash
test-confluence-connection.bat
```

### 调试模式测试

```bash
start-mcp-server-debug.bat
```

### 手动测试

```bash
# 设置环境变量
call confluence-env.bat

# 启动服务器
start-mcp-server.bat
```

## 故障排除

### 常见错误和解决方案

#### 1. 连接被拒绝 (Connection refused)

**可能原因：**
- 服务器地址错误
- 网络连接问题
- 防火墙阻止

**解决方案：**
- 检查 `CONFLUENCE_URL` 是否正确
- 在浏览器中访问Confluence确认可达性
- 检查网络和防火墙设置

#### 2. 认证失败 (401 Unauthorized)

**可能原因：**
- 用户名或密码错误
- API Token无效或过期
- 账户被锁定

**解决方案：**
- 验证用户名和密码
- 重新生成API Token
- 检查账户状态

#### 3. 权限不足 (403 Forbidden)

**可能原因：**
- 账户没有访问权限
- 空间访问受限

**解决方案：**
- 确认账户有Confluence访问权限
- 检查空间权限设置
- 联系管理员分配权限

#### 4. SSL/TLS错误

**可能原因：**
- 证书验证失败
- SSL配置问题

**解决方案：**
- 检查服务器证书
- 临时禁用SSL验证（测试环境）：
  ```batch
  set CONFLUENCE_SSL_VERIFY=false
  ```

#### 5. 超时错误

**可能原因：**
- 网络延迟高
- 服务器响应慢

**解决方案：**
- 增加超时时间：
  ```yaml
  confluence:
    connect-timeout: 60000
    read-timeout: 120000
  ```

## 环境变量参考

| 变量名 | 描述 | 示例 |
|--------|------|------|
| `CONFLUENCE_USERNAME` | 用户名 | `<EMAIL>` |
| `CONFLUENCE_PASSWORD` | 密码或API Token | `ATATTxxxxxxxxxxx` |
| `CONFLUENCE_URL` | 服务器地址 | `https://company.atlassian.net` |
| `CONFLUENCE_SSL_VERIFY` | SSL验证 | `true` 或 `false` |

## 配置文件位置

- **环境变量配置**: `confluence-env.bat`
- **应用配置**: `src/main/resources/application.yml`
- **启动脚本**: `start-mcp-server.bat`
- **调试脚本**: `start-mcp-server-debug.bat`

## 安全建议

1. **使用API Token** 而不是密码
2. **定期轮换** API Token
3. **限制权限** 只授予必要的访问权限
4. **启用SSL验证** 在生产环境
5. **保护配置文件** 不要将认证信息提交到版本控制

## 多环境配置

### 开发环境
```batch
set CONFLUENCE_URL=http://localhost:8090
set CONFLUENCE_SSL_VERIFY=false
```

### 测试环境
```batch
set CONFLUENCE_URL=https://test-confluence.company.com
set CONFLUENCE_SSL_VERIFY=true
```

### 生产环境
```batch
set CONFLUENCE_URL=https://confluence.company.com
set CONFLUENCE_SSL_VERIFY=true
```

## 下一步

配置完成后，您可以：

1. **启动MCP服务器**：`start-mcp-server.bat`
2. **配置多实例**：`quick-multi-setup.bat`
3. **集成到客户端**：使用生成的MCP配置文件

如需更多帮助，请查看其他文档：
- `STARTUP_SOLUTION.md` - 启动问题解决
- `MULTIPLE_INSTANCES_SOLUTION.md` - 多实例配置
- `BUILD_SOLUTION.md` - 构建问题解决
