# 在Augment中配置Confluence MCP数据分析服务器

## 配置步骤

### 1. 准备工作

#### 确保项目已编译
```bash
cd C:/Users/<USER>/Downloads/qccConfluenceMcp
mvn clean package
```

#### 验证JAR包存在
确认以下文件存在：
```
C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

### 2. 配置环境变量

您需要在 `augment-mcp-config.json` 中配置以下环境变量：

#### 必需的数据库配置
```json
{
  "mcpServers": {
    "qccConfluenceMcp": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-jar",
        "C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "你的Confluence用户名",
        "CONFLUENCE_PASSWORD": "你的Confluence密码或API_Token",
        "DATABASE_URL": "*********************************************************************************************",
        "DATABASE_USERNAME": "你的数据库用户名",
        "DATABASE_PASSWORD": "你的数据库密码",
        "DATABASE_DRIVER": "com.mysql.cj.jdbc.Driver"
      }
    }
  }
}
```

### 3. 数据库配置选项

根据您的数据库类型，选择相应的配置：

#### MySQL配置（推荐）
```json
"DATABASE_URL": "*********************************************************************************************",
"DATABASE_DRIVER": "com.mysql.cj.jdbc.Driver"
```

#### PostgreSQL配置
```json
"DATABASE_URL": "*******************************************",
"DATABASE_DRIVER": "org.postgresql.Driver"
```

#### Hive配置（适用于Hadoop环境）
```json
"DATABASE_URL": "*****************************************",
"DATABASE_DRIVER": "org.apache.hive.jdbc.HiveDriver"
```

### 4. 实际配置示例

假设您的数据库信息如下：
- 数据库服务器：localhost:3306
- 数据库名：hadoop_dqp
- 用户名：root
- 密码：123456

完整的配置文件应该是：

```json
{
  "mcpServers": {
    "qccConfluenceMcp": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-jar",
        "C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
      ],
      "env": {
        "CONFLUENCE_USERNAME": "wangjp",
        "CONFLUENCE_PASSWORD": "your_confluence_password",
        "DATABASE_URL": "*********************************************************************************************",
        "DATABASE_USERNAME": "root",
        "DATABASE_PASSWORD": "123456",
        "DATABASE_DRIVER": "com.mysql.cj.jdbc.Driver"
      }
    }
  }
}
```

### 5. 验证配置

#### 手动测试MCP服务器
在配置Augment之前，先手动测试服务器是否能正常启动：

```bash
# 设置环境变量
set DATABASE_URL=*********************************************************************************************
set DATABASE_USERNAME=root
set DATABASE_PASSWORD=123456
set CONFLUENCE_USERNAME=wangjp
set CONFLUENCE_PASSWORD=your_password

# 启动服务器
java -Dspring.ai.mcp.server.transport=STDIO -Dspring.main.web-application-type=none -Dlogging.pattern.console= -jar C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

如果配置正确，您应该看到类似的输出：
```
正在配置数据源...
数据库URL: **************************************
数据源配置完成，连接池名称: HadoopDQPPool
注册Confluence工具到MCP服务器
注册数据分析工具到MCP服务器
```

### 6. 在Augment中使用

配置完成后，重启Augment，然后您可以使用以下功能：

#### Confluence功能
- "搜索Confluence页面关于数据分析的内容"
- "获取页面ID为123的内容"
- "获取空间信息"

#### 数据分析功能
- "获取所有数据库列表"
- "搜索包含user的表"
- "查看applydata.user_info表的结构"
- "执行查询：SELECT * FROM applydata.user_info LIMIT 10"
- "为applydata.user_info表生成count查询"

### 7. 故障排除

#### 常见问题

**问题1：MCP服务器无法启动**
- 检查JAR包路径是否正确
- 确认Java版本是17或更高
- 检查环境变量设置

**问题2：数据库连接失败**
- 验证数据库URL格式
- 检查数据库服务器是否运行
- 确认用户名和密码正确

**问题3：Augment无法连接到MCP服务器**
- 检查配置文件语法是否正确
- 确认所有必需的环境变量都已设置
- 查看Augment的错误日志

#### 调试方法

1. **查看MCP服务器日志**：
   日志文件位置：`./target/confluence-mcp-server-default.log`

2. **启用详细日志**：
   在环境变量中添加：
   ```json
   "LOGGING_LEVEL_COM_QCCCONFLUENCEMCP": "DEBUG"
   ```

3. **测试数据库连接**：
   使用数据库客户端工具验证连接参数

### 8. 安全注意事项

1. **不要在配置文件中使用明文密码**：
   考虑使用环境变量或密钥管理服务

2. **限制数据库权限**：
   为MCP服务器创建专用的数据库用户，只授予SELECT权限

3. **网络安全**：
   确保数据库服务器的网络访问是安全的

### 9. 高级配置

#### 连接池调优
如果需要调优连接池，可以添加以下JVM参数：

```json
"args": [
  "-Dspring.ai.mcp.server.transport=STDIO",
  "-Dspring.main.web-application-type=none",
  "-Dlogging.pattern.console=",
  "-Ddatasource.hikari.maximum-pool-size=5",
  "-Ddatasource.hikari.minimum-idle=1",
  "-jar",
  "C:/Users/<USER>/Desktop/idea/qccConfluenceMcp/target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar"
]
```

#### 多数据库支持
如果需要连接多个数据库，可以创建多个MCP服务器实例：

```json
{
  "mcpServers": {
    "qccConfluenceMcp-mysql": {
      "command": "java",
      "args": [...],
      "env": {
        "DATABASE_URL": "**************************************",
        ...
      }
    },
    "qccConfluenceMcp-postgres": {
      "command": "java",
      "args": [...],
      "env": {
        "DATABASE_URL": "******************************************",
        ...
      }
    }
  }
}
```

## 完成配置

按照以上步骤配置完成后，您就可以在Augment中使用Confluence文档管理和数据分析功能了。MCP服务器将提供以下工具：

- **Confluence工具**：搜索页面、获取内容、管理空间
- **数据分析工具**：搜索表、查看结构、执行SQL查询、生成快捷SQL

这些工具将完全兼容您现有的Hadoop数据分析平台工作流程。
