@echo off
REM Confluence配置验证脚本

echo ========================================
echo Confluence MCP 配置验证
echo ========================================
echo.

set VALIDATION_PASSED=1

echo 正在验证配置...
echo.

REM 1. 检查配置文件
echo [1/6] 检查配置文件
echo ----------------------------------------

if exist "confluence-env.bat" (
    echo ✓ confluence-env.bat 存在
    call confluence-env.bat
) else (
    echo ✗ confluence-env.bat 不存在
    echo   建议: 运行 configure-confluence.bat 创建配置
    set VALIDATION_PASSED=0
)

if exist "src\main\resources\application.yml" (
    echo ✓ application.yml 存在
) else (
    echo ✗ application.yml 不存在
    set VALIDATION_PASSED=0
)

echo.

REM 2. 检查环境变量
echo [2/6] 检查环境变量
echo ----------------------------------------

if not "%CONFLUENCE_USERNAME%"=="" (
    echo ✓ CONFLUENCE_USERNAME: %CONFLUENCE_USERNAME%
) else (
    echo ✗ CONFLUENCE_USERNAME 未设置
    set VALIDATION_PASSED=0
)

if not "%CONFLUENCE_PASSWORD%"=="" (
    echo ✓ CONFLUENCE_PASSWORD: [已设置]
) else (
    echo ✗ CONFLUENCE_PASSWORD 未设置
    set VALIDATION_PASSED=0
)

if not "%CONFLUENCE_URL%"=="" (
    echo ✓ CONFLUENCE_URL: %CONFLUENCE_URL%
) else (
    echo ✓ CONFLUENCE_URL: [使用默认值]
    set CONFLUENCE_URL=https://doc.greatld.com
)

echo.

REM 3. 检查JAR文件
echo [3/6] 检查JAR文件
echo ----------------------------------------

if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo ✓ JAR文件存在
    for %%I in (target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar) do echo   大小: %%~zI 字节
) else (
    echo ✗ JAR文件不存在
    echo   建议: 运行 simple-rebuild.bat 构建项目
    set VALIDATION_PASSED=0
)

echo.

REM 4. 检查Java环境
echo [4/6] 检查Java环境
echo ----------------------------------------

java -version >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ Java可用
    for /f "tokens=3" %%g in ('java -version 2^>^&1 ^| findstr /i "version"') do (
        echo   版本: %%g
    )
) else (
    echo ✗ Java不可用
    echo   建议: 安装Java 17或更高版本
    set VALIDATION_PASSED=0
)

echo.

REM 5. 验证URL格式
echo [5/6] 验证URL格式
echo ----------------------------------------

echo %CONFLUENCE_URL% | findstr /r "^https\?://" >nul
if %ERRORLEVEL% equ 0 (
    echo ✓ URL格式正确: %CONFLUENCE_URL%
) else (
    echo ✗ URL格式错误: %CONFLUENCE_URL%
    echo   建议: 使用 http:// 或 https:// 开头
    set VALIDATION_PASSED=0
)

echo.

REM 6. 检查网络连通性
echo [6/6] 检查网络连通性
echo ----------------------------------------

echo 正在测试网络连接...

REM 提取主机名
for /f "tokens=3 delims=/" %%a in ("%CONFLUENCE_URL%") do set HOST=%%a

ping -n 1 %HOST% >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✓ 可以连接到 %HOST%
) else (
    echo ⚠ 无法ping通 %HOST%
    echo   这可能是正常的（服务器可能禁用ping）
)

echo.

REM 验证结果
echo ========================================
echo 验证结果
echo ========================================
echo.

if %VALIDATION_PASSED% equ 1 (
    echo ✅ 配置验证通过！
    echo.
    echo 您可以：
    echo 1. 启动MCP服务器: start-mcp-server.bat
    echo 2. 测试连接: test-confluence-connection.bat
    echo 3. 调试模式启动: start-mcp-server-debug.bat
    echo.
) else (
    echo ❌ 配置验证失败！
    echo.
    echo 建议的修复步骤：
    echo 1. 运行配置向导: configure-confluence.bat
    echo 2. 构建项目: simple-rebuild.bat
    echo 3. 重新验证: validate-config.bat
    echo.
)

echo 配置摘要:
echo ----------------------------------------
echo 用户名: %CONFLUENCE_USERNAME%
echo 服务器: %CONFLUENCE_URL%
echo 密码: %CONFLUENCE_PASSWORD:~0,3%***
echo JAR文件: %~dp0target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
echo.

echo 相关脚本:
echo ----------------------------------------
echo configure-confluence.bat     - 配置向导
echo test-confluence-connection.bat - 连接测试
echo start-mcp-server.bat         - 启动服务器
echo start-mcp-server-debug.bat   - 调试启动
echo simple-rebuild.bat           - 重新构建
echo.

pause
