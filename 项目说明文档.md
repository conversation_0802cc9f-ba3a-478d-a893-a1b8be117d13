# Hadoop数据分析MCP服务器

## 项目概述

这是一个基于Spring Boot和Spring AI MCP Server的Hadoop数据分析服务器，专门为Hadoop数据分析平台（https://hadoop.greatld.com/dqp/dataAnalysis）提供MCP（Model Context Protocol）接口支持。

该项目通过Web方式访问Hadoop数据分析平台，提供表查询、结构获取、SQL生成等功能，可以与支持MCP的AI助手（如Augment、Claude Desktop）集成使用。

## 主要功能

### 数据分析功能
- **平台登录** (`login`): 登录到Hadoop数据分析平台获取访问权限
- **获取数据库列表** (`getDatabaseList`): 获取所有可用的数据库列表
- **搜索数据库表** (`searchTables`): 搜索表，支持库名.表名格式
- **获取表结构** (`getTableStructure`): 获取指定表的结构信息
- **生成快捷SQL** (`generateQuickSQL`): 生成select、count、describe等快捷SQL
- **SQL执行准备** (`executeSQL`): 准备SQL语句并提供执行指导
- **连接状态检查** (`getConnectionStatus`): 获取平台连接状态和基本信息

## 技术架构

### 核心技术栈
- **Spring Boot 3.4.1**: 应用框架
- **Spring AI 1.0.1**: AI集成框架
- **Spring AI MCP Server**: MCP协议实现
- **Apache HttpClient 5**: HTTP客户端
- **Jsoup**: HTML解析器
- **Jackson**: JSON处理
- **SLF4J + Logback**: 日志框架

### 访问方式
- **Web方式**: 通过HTTP请求访问Hadoop数据分析平台
- **会话管理**: 自动处理登录和会话保持
- **HTML解析**: 解析页面内容获取数据库和表信息

## 项目结构

```
src/main/java/com/hadoopdataanalysis/
├── HadoopDataAnalysisApplication.java    # 主应用类
└── DataAnalysisService.java              # 数据分析服务类

src/main/resources/
└── application.yml                       # 配置文件
```

## 配置说明

### 环境变量配置

#### Hadoop平台登录配置（必需）
```bash
HADOOP_USERNAME=你的Hadoop平台用户名
HADOOP_PASSWORD=你的Hadoop平台密码
```

#### 平台地址配置（可选）
```bash
HADOOP_URL=https://hadoop.greatld.com
MCP_SERVER_NAME=hadoop-data-analysis-mcp
MCP_INSTANCE_ID=default
```

### 配置文件说明

主要配置在 `application.yml` 中：

- **Spring AI MCP配置**: 启用MCP服务器功能
- **Hadoop平台配置**: 平台地址和路径配置
- **会话配置**: 登录会话管理参数
- **日志配置**: 日志级别和输出格式

## 使用指南

### 1. 环境准备
- Java 17+
- Maven 3.6+
- Hadoop数据分析平台访问权限

### 2. 配置设置
1. 设置必要的环境变量：
   ```bash
   HADOOP_USERNAME=你的用户名
   HADOOP_PASSWORD=你的密码
   ```

### 3. 编译和启动
```bash
# 编译项目
mvn clean package

# 启动服务
java -jar target/hadoop-data-analysis-mcp-1.0.0-SNAPSHOT.jar
```

### 4. Augment配置
在Augment中配置MCP服务器：
```json
{
  "mcpServers": {
    "hadoop-data-analysis": {
      "command": "java",
      "args": [
        "-Dspring.ai.mcp.server.transport=STDIO",
        "-Dspring.main.web-application-type=none",
        "-Dlogging.pattern.console=",
        "-jar",
        "C:/path/to/your/target/hadoop-data-analysis-mcp-1.0.0-SNAPSHOT.jar"
      ],
      "env": {
        "HADOOP_USERNAME": "你的Hadoop平台用户名",
        "HADOOP_PASSWORD": "你的Hadoop平台密码"
      }
    }
  }
}
```

## API工具说明

### 数据分析工具
- `login()`: 登录到Hadoop数据分析平台
- `getDatabaseList()`: 获取数据库列表
- `searchTables(tableNamePattern, schemaName, limit)`: 搜索表
- `getTableStructure(tableName)`: 获取表结构
- `generateQuickSQL(tableName, sqlType)`: 生成快捷SQL
- `executeSQL(sql, maxRows)`: 准备SQL执行
- `getConnectionStatus()`: 获取连接状态

## 使用示例

### 在Augment中的使用
配置完成后，您可以在Augment中使用以下命令：

1. **登录平台**：
   - "登录到Hadoop数据分析平台"

2. **查看数据库**：
   - "获取所有数据库列表"
   - "显示可用的数据库"

3. **搜索表**：
   - "搜索包含user的表"
   - "在applydata数据库中搜索表"

4. **查看表结构**：
   - "查看applydata.user_info表的结构"
   - "获取表结构信息"

5. **生成SQL**：
   - "为applydata.user_info表生成select查询"
   - "生成count查询语句"

6. **检查状态**：
   - "检查平台连接状态"
   - "显示当前配置信息"

## 安全特性

### 访问安全
- **登录验证**: 需要有效的用户名和密码
- **会话管理**: 自动处理登录状态和会话保持
- **SQL安全**: 只允许SELECT、DESCRIBE、SHOW等安全查询

### 数据保护
- **敏感信息**: 密码等敏感信息通过环境变量配置
- **会话隔离**: 每个实例独立的会话管理
- **访问限制**: 只能访问用户有权限的数据

## 故障排除

### 常见问题

1. **登录失败**：
   - 检查用户名和密码是否正确
   - 确认Hadoop平台是否可访问
   - 验证网络连接

2. **无法获取数据库列表**：
   - 确保已成功登录
   - 检查用户是否有相应权限
   - 验证平台页面结构是否发生变化

3. **MCP连接问题**：
   - 确认JAR包路径正确
   - 检查环境变量设置
   - 查看服务器日志

### 日志查看
日志文件位置：`./target/hadoop-data-analysis-mcp-{instance-id}.log`

### 调试模式
设置环境变量启用调试：
```bash
LOGGING_LEVEL_COM_HADOOPDATAANALYSIS=DEBUG
```

## 开发说明

### 添加新功能
1. 在DataAnalysisService中添加方法
2. 使用 `@Tool` 注解标记工具方法
3. 在Application类中确保服务已注册
4. 更新文档

### 测试
```bash
# 运行测试
mvn test

# 手动测试登录
curl -X POST http://localhost:8080/test-login
```

## 版本信息
- **当前版本**: 1.0.0-SNAPSHOT
- **Spring Boot版本**: 3.4.1
- **Spring AI版本**: 1.0.1
- **Java版本**: 17+
- **目标平台**: Hadoop数据分析平台 (hadoop.greatld.com)

## 项目特点

- **纯净独立**: 专注于数据分析功能，无其他依赖
- **Web方式访问**: 通过页面登录方式，无需直接数据库连接
- **安全可靠**: 基于用户权限的安全访问机制
- **易于配置**: 只需配置用户名和密码即可使用
- **标准MCP**: 完全兼容MCP协议，可与各种AI助手集成
