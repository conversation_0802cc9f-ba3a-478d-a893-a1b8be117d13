# Confluence MCP 连接配置总结

## 🎯 配置完成情况

我已经为您创建了完整的Confluence连接配置工具和文档。现在您可以轻松配置和管理Confluence MCP服务器的连接。

## 📋 可用的配置工具

### 1. 自动配置向导
```bash
configure-confluence.bat
```
**功能：**
- 交互式配置向导
- 支持密码和API Token认证
- 自动测试连接
- 生成配置文件

### 2. 简化配置工具
```bash
setup-confluence-simple.bat
```
**功能：**
- 简化的配置流程
- 英文界面，避免编码问题
- 快速生成环境变量文件

### 3. 连接测试工具
```bash
test-confluence-connection.bat
```
**功能：**
- 验证连接配置
- 多种测试模式
- 详细的错误诊断

### 4. 配置验证工具
```bash
validate-config.bat
```
**功能：**
- 全面的配置检查
- 环境验证
- 问题诊断和建议

## 🚀 快速开始步骤

### 步骤1：运行配置向导
```bash
setup-confluence-simple.bat
```

### 步骤2：输入连接信息
- **服务器地址**: `https://doc.greatld.com` (或您的Confluence服务器)
- **用户名**: 您的Confluence用户名
- **认证**: 选择密码或API Token

### 步骤3：测试连接
```bash
test-confluence-connection.bat
```

### 步骤4：启动MCP服务器
```bash
start-mcp-server.bat
```

## 🔐 推荐的认证配置

### API Token认证（推荐）

**获取API Token：**
1. 登录Confluence
2. 点击右上角头像 → 账户设置
3. 安全 → 创建和管理API令牌
4. 创建新令牌并复制

**配置示例：**
```bash
CONFLUENCE_USERNAME=<EMAIL>
CONFLUENCE_PASSWORD=ATATTxxxxxxxxxxxxxxxxxxx
CONFLUENCE_URL=https://doc.greatld.com
```

## 📁 生成的配置文件

### confluence-env.bat
```batch
@echo off
set CONFLUENCE_USERNAME=your_username
set CONFLUENCE_PASSWORD=your_password_or_token
set CONFLUENCE_URL=https://doc.greatld.com

echo Confluence environment variables loaded:
echo - Username: %CONFLUENCE_USERNAME%
echo - Server: %CONFLUENCE_URL%
```

### 使用配置文件
```bash
# 加载环境变量
call confluence-env.bat

# 启动MCP服务器
start-mcp-server.bat
```

## 🔧 高级配置选项

### 自定义超时设置
在 `src/main/resources/application.yml` 中：
```yaml
confluence:
  connect-timeout: 30000  # 连接超时（毫秒）
  read-timeout: 60000     # 读取超时（毫秒）
  ssl-verification: true  # SSL验证
```

### 环境变量覆盖
```bash
set CONFLUENCE_URL=https://your-server.com
set CONFLUENCE_SSL_VERIFY=false  # 禁用SSL验证（仅测试）
```

## 🐛 故障排除

### 常见问题和解决方案

#### 1. 认证失败 (401 Unauthorized)
```bash
# 检查用户名和密码
echo Username: %CONFLUENCE_USERNAME%
echo Password: %CONFLUENCE_PASSWORD:~0,3%***

# 重新配置
setup-confluence-simple.bat
```

#### 2. 连接超时
```bash
# 检查网络连接
ping doc.greatld.com

# 增加超时时间（在application.yml中）
confluence:
  connect-timeout: 60000
  read-timeout: 120000
```

#### 3. SSL证书错误
```bash
# 临时禁用SSL验证（仅测试环境）
set CONFLUENCE_SSL_VERIFY=false
```

## 📊 配置验证清单

运行验证脚本检查配置：
```bash
validate-config.bat
```

**检查项目：**
- ✅ 配置文件存在
- ✅ 环境变量设置
- ✅ JAR文件构建
- ✅ Java环境
- ✅ URL格式
- ✅ 网络连通性

## 🔄 多实例配置

配置完成后，可以设置多实例：

### 1. 快速多实例设置
```bash
quick-multi-setup.bat
```

### 2. 高级实例管理
```bash
manage-multiple-instances.bat
```

## 📖 相关文档

- **`CONFLUENCE_CONFIG_GUIDE.md`** - 详细配置指南
- **`STARTUP_SOLUTION.md`** - 启动问题解决
- **`MULTIPLE_INSTANCES_SOLUTION.md`** - 多实例配置
- **`BUILD_SOLUTION.md`** - 构建问题解决

## 🎉 配置完成后的使用

### 启动MCP服务器
```bash
# 普通模式
start-mcp-server.bat

# 调试模式
start-mcp-server-debug.bat
```

### 验证MCP工具
启动后，MCP服务器将提供以下工具：
- `getConfluencePage` - 获取页面内容
- `searchConfluence` - 搜索内容
- `getSpaceInfo` - 获取空间信息
- `getSpacePages` - 获取空间页面列表

### 集成到客户端
使用生成的MCP配置文件将服务器集成到支持MCP的客户端中。

## 💡 最佳实践

1. **使用API Token** 而不是密码
2. **定期轮换** 认证凭据
3. **启用SSL验证** 在生产环境
4. **监控连接** 使用调试模式排查问题
5. **备份配置** 保存配置文件副本

现在您已经拥有了完整的Confluence连接配置工具集！开始配置您的连接吧！
