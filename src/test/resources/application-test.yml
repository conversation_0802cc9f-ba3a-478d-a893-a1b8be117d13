# 测试环境配置
spring:
  application:
    name: confluence-mcp-server-test
  
  main:
    web-application-type: none
  
  ai:
    mcp:
      server:
        enabled: true
        stdio: false  # 测试环境禁用STDIO
        name: confluence-mcp-server-test
        version: 1.0.0-TEST
        type: SYNC
        capabilities:
          tool: true
          resource: false
          prompt: false
          completion: false

# 测试用Confluence配置
confluence:
  base-url: https://doc.greatld.com
  username: ${CONFLUENCE_USERNAME:test_user}
  password: ${CONFLUENCE_PASSWORD:test_password}
  connect-timeout: 5000
  read-timeout: 10000
  ssl-verification: true

# 测试日志配置
logging:
  level:
    com.qccconfluencemcp: DEBUG
    org.springframework.ai: INFO
    org.springframework.ai.mcp: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
