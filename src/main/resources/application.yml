# Hadoop数据分析MCP服务器配置
spring:
  application:
    name: ${MCP_SERVER_NAME:hadoop-data-analysis-mcp}

  # Web应用配置
  main:
    banner-mode: off
    web-application-type: none

  # JMX配置（避免多实例冲突）
  jmx:
    enabled: false

  # Spring AI MCP服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: ${MCP_SERVER_NAME:hadoop-data-analysis-mcp}
        version: 1.0.0
        type: SYNC
        # 变更通知
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true
        # 添加实例ID以避免冲突
        instance-id: ${MCP_INSTANCE_ID:${random.uuid}}

# 多实例支持配置
server:
  port: 0  # 随机端口，避免端口冲突

# Hadoop数据分析平台连接配置
hadoop:
  # 数据分析平台地址
  base-url: ${HADOOP_URL:https://hadoop.greatld.com}

  # 登录认证信息
  username: ${HADOOP_USERNAME:}
  password: ${HADOOP_PASSWORD:}

  # 数据分析页面路径
  analysis-path: /dqp/dataAnalysis

  # 连接超时配置（毫秒）
  connect-timeout: 30000
  read-timeout: 60000

  # 会话配置
  session:
    timeout: 1800000  # 30分钟会话超时
    keep-alive: true

  # 重试配置
  retry:
    max-attempts: 3
    delay: 1000

# 日志配置
logging:
  level:
    com.hadoopdataanalysis: INFO
    org.springframework.ai: INFO
    org.springframework.ai.mcp: DEBUG
  # STDIO模式需要清空控制台日志格式（生产环境）
  pattern:
    console: ""
  file:
    name: ./target/hadoop-data-analysis-mcp-${MCP_INSTANCE_ID:default}.log
