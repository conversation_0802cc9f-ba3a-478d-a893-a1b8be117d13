# 开发环境配置
spring:
  application:
    name: confluence-mcp-server-dev

  # Web应用配置
  main:
    banner-mode: console

  # Spring AI MCP服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: confluence-mcp-server-dev
        version: 1.0.0-DEV
        type: SYNC
        # 开发环境启用所有通知
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true

# Confluence连接配置
confluence:
  # Confluence服务器地址
  base-url: https://doc.greatld.com

  # 认证信息（请在环境变量中设置）
  username: ${CONFLUENCE_USERNAME:}
  password: ${CONFLUENCE_PASSWORD:}

  # 连接超时配置
  connect-timeout: 30000
  read-timeout: 60000

  # SSL验证
  ssl-verification: true

# 开发环境日志配置 - 详细日志输出
logging:
  level:
    com.qccconfluencemcp: DEBUG
    org.springframework.ai: DEBUG
    org.springframework.ai.mcp: DEBUG
    org.springframework.web.reactive.function.client: DEBUG
    org.springframework.boot: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ./target/confluence-mcp-server-dev.log
