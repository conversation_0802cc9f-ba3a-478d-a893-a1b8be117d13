package com.hadoopdataanalysis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

/**
 * Hadoop数据分析MCP服务器主应用类
 * <p>
 * 基于Spring AI MCP Server Boot Starter构建的数据分析服务器
 * 提供Hadoop数据分析平台的表查询、结构获取、SQL执行等功能
 * <p>
 * 支持多实例运行，通过环境变量区分不同实例
 */
@SpringBootApplication
public class HadoopDataAnalysisApplication {

    private static final Logger logger = LoggerFactory.getLogger(HadoopDataAnalysisApplication.class);

    @Value("${MCP_SERVER_NAME:hadoop-data-analysis-mcp}")
    private String serverName;

    @Value("${MCP_INSTANCE_ID:default}")
    private String instanceId;

    /**
     * 应用程序入口点
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // 设置系统属性以支持多实例
        System.setProperty("spring.application.name",
                System.getProperty("MCP_SERVER_NAME", "hadoop-data-analysis-mcp"));

        // 禁用JMX以避免多实例冲突
        System.setProperty("spring.jmx.enabled", "false");

        // 设置随机端口
        if (System.getProperty("server.port") == null) {
            System.setProperty("server.port", "0");
        }

        logger.info("启动Hadoop数据分析MCP服务器实例: {}",
                System.getProperty("MCP_SERVER_NAME", "hadoop-data-analysis-mcp"));
        logger.info("实例ID: {}",
                System.getProperty("MCP_INSTANCE_ID", "default"));

        SpringApplication.run(HadoopDataAnalysisApplication.class, args);
    }

    /**
     * 注册数据分析工具到MCP服务器
     *
     * @param dataAnalysisService 数据分析服务实例
     * @return ToolCallbackProvider 数据分析工具回调提供者
     */
    @Bean
    public ToolCallbackProvider dataAnalysisTools(DataAnalysisService dataAnalysisService) {
        logger.info("注册数据分析工具到MCP服务器实例: {} (ID: {})", serverName, instanceId);
        return MethodToolCallbackProvider.builder()
                .toolObjects(dataAnalysisService)
                .build();
    }
}
