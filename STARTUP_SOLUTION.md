# Confluence MCP 启动问题解决方案

## 问题描述

在尝试启动Confluence MCP应用时遇到了以下错误：
```
org.yaml.snakeyaml.constructor.DuplicateKeyException: while constructing a mapping
found duplicate key spring
```

## 问题原因

**YAML配置文件中存在重复的`spring:`键**

在`src/main/resources/application.yml`文件中，有多个`spring:`配置块：
1. 第2行的主要spring配置
2. 第47行的JMX配置中又定义了一个spring块
3. 第64行在YAML文档分隔符后又有一个spring块

YAML解析器不允许在同一个文档中有重复的顶级键。

## 解决方案

### 1. 重新组织YAML配置结构

将所有spring相关的配置合并到一个`spring:`块中：

```yaml
# 正确的配置结构
spring:
  application:
    name: ${MCP_SERVER_NAME:confluence-mcp-server}
  
  main:
    banner-mode: off
    web-application-type: none
  
  # JMX配置合并到这里
  jmx:
    enabled: false
  
  ai:
    mcp:
      server:
        enabled: true
        name: ${MCP_SERVER_NAME:confluence-mcp-server}
        version: 1.0.0
        type: SYNC
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true
        instance-id: ${MCP_INSTANCE_ID:${random.uuid}}
```

### 2. 删除重复的配置块

移除了以下重复的配置：
- 第47行的独立spring/jmx配置块
- 第64行YAML文档分隔符后的spring配置块

### 3. 简化配置文件

最终的`application.yml`文件结构：
```yaml
# Confluence MCP服务器配置
spring:
  # 所有spring配置都在这里

# 服务器配置
server:
  port: 0

# Confluence配置
confluence:
  base-url: https://doc.greatld.com
  # 其他配置...

# 日志配置
logging:
  level:
    com.qccconfluencemcp: INFO
  # 其他配置...
```

## 验证解决方案

### 1. 重新构建项目

```bash
set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1
mvnw.cmd clean package -DskipTests
```

### 2. 测试启动

```bash
java -jar target/qccConfluenceMcp-0.0.1-SNAPSHOT.jar
```

### 3. 成功启动的标志

看到以下日志表示启动成功：
```
INFO com.qccconfluencemcp.ConfluenceMcpApplication -- 启动Confluence MCP服务器实例: confluence-mcp-server
INFO com.qccconfluencemcp.ConfluenceMcpApplication -- 实例ID: default
```

## 常见YAML配置错误

### 1. 重复键错误
```yaml
# 错误示例
spring:
  application:
    name: app1

spring:  # 重复的键！
  jmx:
    enabled: false
```

### 2. 正确的合并方式
```yaml
# 正确示例
spring:
  application:
    name: app1
  jmx:
    enabled: false
```

### 3. 使用YAML文档分隔符
```yaml
# 主配置
spring:
  application:
    name: app1

---
# 不同profile的配置
spring:
  config:
    activate:
      on-profile: "dev"
  # 这里可以有spring配置，因为是不同的YAML文档
```

## 预防措施

### 1. 使用YAML验证工具
在修改配置文件前，使用在线YAML验证器检查语法。

### 2. 配置文件结构规范
- 将相关配置组织在一起
- 避免重复的顶级键
- 使用注释说明配置用途

### 3. 测试配置更改
每次修改配置文件后，立即测试应用启动。

## 多实例配置

现在启动问题已解决，可以使用之前创建的多实例解决方案：

### 1. 快速多实例配置
```bash
quick-multi-setup.bat
```

### 2. 高级实例管理
```bash
manage-multiple-instances.bat
```

### 3. 简单重建脚本
```bash
simple-rebuild.bat
```

## 总结

通过重新组织YAML配置文件结构，将所有spring相关配置合并到单一的`spring:`块中，成功解决了启动时的重复键错误。现在应用可以正常启动，并支持多实例运行配置。

### 关键修复点：
1. ✅ 合并重复的spring配置块
2. ✅ 移除冗余的YAML文档分隔符
3. ✅ 简化配置文件结构
4. ✅ 验证YAML语法正确性

### 下一步：
- 配置Confluence连接信息
- 测试MCP工具功能
- 部署多实例配置
