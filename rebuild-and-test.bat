@echo off
REM 重新构建并测试启动

echo ========================================
echo Rebuilding and Testing Confluence MCP
echo ========================================
echo.

REM 设置JAVA_HOME
set JAVA_HOME=C:\Program Files\Java\jdk-17.0.1
echo JAVA_HOME set to: %JAVA_HOME%
echo.

REM 清理并重新构建
echo Step 1: Cleaning and rebuilding...
call mvnw.cmd clean package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Checking JAR file...
if exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo ✓ JAR file exists
    for %%I in (target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar) do echo   Size: %%~zI bytes
) else (
    echo ✗ JAR file not found
    pause
    exit /b 1
)

echo.
echo Step 3: Testing application startup...
echo Setting test environment variables...
set CONFLUENCE_USERNAME=test_user
set CONFLUENCE_PASSWORD=test_password

echo.
echo Starting application (will auto-stop after 5 seconds)...
echo Press Ctrl+C to stop manually if needed
echo.

REM 启动应用并在5秒后停止
start /b java -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar
timeout /t 5 /nobreak >nul
taskkill /f /im java.exe >nul 2>&1

echo.
echo Test completed. Check the output above for any errors.
echo.
echo If you see Spring Boot startup logs without errors, the application is working correctly.
echo.
pause
