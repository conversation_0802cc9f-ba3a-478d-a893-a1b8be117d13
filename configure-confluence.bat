@echo off
REM Confluence连接配置向导

echo ========================================
echo Confluence MCP 连接配置向导
echo ========================================
echo.

echo 此向导将帮助您配置Confluence连接信息
echo.

REM 获取Confluence服务器信息
echo 步骤 1: Confluence服务器配置
echo ----------------------------------------
echo.

set /p CONFLUENCE_URL="请输入Confluence服务器地址 (默认: https://doc.greatld.com): "
if "%CONFLUENCE_URL%"=="" set CONFLUENCE_URL=https://doc.greatld.com

echo.
echo 步骤 2: 认证信息配置
echo ----------------------------------------
echo.

echo 认证方式选择:
echo 1. 用户名 + 密码
echo 2. 用户名 + API Token (推荐)
echo.

set /p AUTH_TYPE="请选择认证方式 (1 或 2): "

if "%AUTH_TYPE%"=="2" (
    echo.
    echo 使用API Token认证 (推荐)
    echo.
    echo 如何获取API Token:
    echo 1. 登录Confluence
    echo 2. 点击右上角头像 ^> 账户设置
    echo 3. 选择"安全" ^> "创建和管理API令牌"
    echo 4. 点击"创建API令牌"
    echo 5. 输入标签名称，点击"创建"
    echo 6. 复制生成的令牌
    echo.
)

set /p CONFLUENCE_USERNAME="请输入Confluence用户名: "

if "%AUTH_TYPE%"=="2" (
    set /p CONFLUENCE_PASSWORD="请输入API Token: "
) else (
    set /p CONFLUENCE_PASSWORD="请输入密码: "
)

echo.
echo 步骤 3: 连接测试配置
echo ----------------------------------------
echo.

set /p TEST_SPACE="请输入一个测试空间键 (用于验证连接，如: DEMO): "

echo.
echo 配置摘要:
echo ----------------------------------------
echo 服务器地址: %CONFLUENCE_URL%
echo 用户名: %CONFLUENCE_USERNAME%
if "%AUTH_TYPE%"=="2" (
    echo 认证方式: API Token
) else (
    echo 认证方式: 密码
)
echo 测试空间: %TEST_SPACE%
echo.

set /p CONFIRM="确认配置正确? (y/n): "
if /i not "%CONFIRM%"=="y" (
    echo 配置已取消
    pause
    exit /b 0
)

echo.
echo 步骤 4: 保存配置
echo ----------------------------------------
echo.

REM 创建环境变量设置脚本
(
    echo @echo off
    echo REM Confluence连接配置
    echo REM 由configure-confluence.bat自动生成
    echo.
    echo set CONFLUENCE_USERNAME=%CONFLUENCE_USERNAME%
    echo set CONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD%
    echo set CONFLUENCE_URL=%CONFLUENCE_URL%
    echo set TEST_SPACE=%TEST_SPACE%
    echo.
    echo echo Confluence环境变量已设置:
    echo echo - 用户名: %%CONFLUENCE_USERNAME%%
    echo echo - 服务器: %%CONFLUENCE_URL%%
    echo echo - 测试空间: %%TEST_SPACE%%
) > confluence-env.bat

echo ✓ 配置已保存到: confluence-env.bat

REM 更新application.yml中的服务器地址
if not "%CONFLUENCE_URL%"=="https://doc.greatld.com" (
    echo.
    echo 正在更新application.yml中的服务器地址...
    
    powershell -Command "(Get-Content src\main\resources\application.yml) -replace 'base-url: https://doc.greatld.com', 'base-url: %CONFLUENCE_URL%' | Set-Content src\main\resources\application.yml"
    
    echo ✓ application.yml已更新
)

echo.
echo 步骤 5: 测试连接
echo ----------------------------------------
echo.

set /p TEST_NOW="是否现在测试连接? (y/n): "
if /i not "%TEST_NOW%"=="y" goto :skip_test

echo.
echo 正在测试连接...

REM 设置环境变量
call confluence-env.bat

REM 检查JAR文件是否存在
if not exist "target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar" (
    echo 正在构建项目...
    call simple-rebuild.bat
    if %ERRORLEVEL% neq 0 (
        echo 构建失败，无法测试连接
        goto :skip_test
    )
)

echo.
echo 启动MCP服务器进行连接测试...
echo 服务器将在10秒后自动停止
echo.

REM 启动服务器并在10秒后停止
start /b java -Dspring.ai.mcp.server.transport=STDIO ^
     -Dspring.main.web-application-type=none ^
     -DCONFLUENCE_USERNAME=%CONFLUENCE_USERNAME% ^
     -DCONFLUENCE_PASSWORD=%CONFLUENCE_PASSWORD% ^
     -jar target\qccConfluenceMcp-0.0.1-SNAPSHOT.jar

timeout /t 10 /nobreak >nul
taskkill /f /im java.exe >nul 2>&1

echo.
echo 连接测试完成

:skip_test

echo.
echo ========================================
echo 配置完成！
echo ========================================
echo.

echo 下一步操作:
echo.
echo 1. 使用配置启动服务器:
echo    call confluence-env.bat
echo    start-mcp-server.bat
echo.
echo 2. 配置多实例:
echo    quick-multi-setup.bat
echo.
echo 3. 手动设置环境变量:
echo    call confluence-env.bat
echo.

echo 配置文件位置:
echo - 环境变量: confluence-env.bat
echo - 应用配置: src\main\resources\application.yml
echo.

pause
